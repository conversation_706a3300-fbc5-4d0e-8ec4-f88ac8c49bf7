# 微信用户接口文档

## 微信用户信息更新接口 (已弃用)

> **注意**: 该接口已被标记为弃用状态，请使用新的[微信用户信息解密接口](#微信用户信息解密接口)替代。将在未来版本中移除此接口。

该接口用于更新已登录微信用户的个人信息，包括昵称、头像等基本资料。

### 接口基本信息

- **接口路径**：`/api/update_user_info/`
- **请求方式**：POST
- **认证方式**：JWT Token (Bearer Authentication)
- **数据格式**：JSON

### 请求参数

#### 请求头

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| Authorization | String | 是 | JWT认证令牌，格式为"Bearer {token}" |

#### 请求体

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| openid | String | 是 | 用户的微信OpenID |
| nickname | String | 否 | 用户昵称 |
| avatar_url | String | 否 | 用户头像URL |
| gender | Integer | 否 | 用户性别（0：未知，1：男，2：女） |
| country | String | 否 | 用户所在国家 |
| province | String | 否 | 用户所在省份 |
| city | String | 否 | 用户所在城市 |

### 响应结果

#### 成功响应

```json
{
    "success": true
}
```

#### 错误响应

```json
{
    "success": false,
    "message": "错误信息"
}
```

### 错误码说明

| 状态码 | 错误信息 | 描述 |
|-------|---------|------|
| 400 | 无效的openid | 请求中的openid不存在或与token中的openid不匹配 |
| 401 | 无效的认证头 | Authorization头部格式错误 |
| 401 | 登录已过期 | JWT token已过期 |
| 401 | 无效的token | JWT token无效 |
| 404 | 用户不存在 | 指定openid的用户不存在 |
| 500 | 更新失败 | 服务器内部错误 |

### 请求示例

```
POST /api/update_user_info/ HTTP/1.1
Host: api.example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
    "openid": "oXw1G5Jq9xZT6Kcu8L_wP7F2iI0g",
    "nickname": "张三",
    "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/...",
    "gender": 1,
    "country": "中国",
    "province": "广东",
    "city": "深圳"
}
```

### 响应示例

```
HTTP/1.1 200 OK
Content-Type: application/json

{
    "success": true
}
```

## 微信用户信息解密接口

该接口用于直接从微信获取用户信息，通过解密小程序前端获取的加密数据。

### 接口基本信息

- **接口路径**：`/api/user/info/wx/`
- **请求方式**：POST
- **认证方式**：JWT Token (Bearer Authentication)
- **数据格式**：JSON

### 请求参数

#### 请求头

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| Authorization | String | 是 | JWT认证令牌，格式为"Bearer {token}" |

#### 请求体

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| code | String | 是 | 通过wx.login获取的临时登录凭证 |
| encryptedData | String | 是 | 通过wx.getUserProfile获取的加密用户信息 |
| iv | String | 是 | 加密算法的初始向量 |

### 响应结果

#### 成功响应

```json
{
    "success": true,
    "userInfo": {
        "openid": "oXw1G5Jq9xZT6Kcu8L_wP7F2iI0g",
        "nickname": "张三",
        "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/...",
        "gender": 1,
        "country": "中国",
        "province": "广东",
        "city": "深圳"
    }
}
```

#### 错误响应

```json
{
    "success": false,
    "message": "错误信息",
    "detail": "详细错误信息"
}
```

### 错误码说明

| 状态码 | 错误信息 | 描述 |
|-------|---------|------|
| 400 | 缺少必要参数 | 缺少code、encryptedData或iv参数 |
| 400 | 微信接口调用失败 | 调用微信接口返回错误 |
| 400 | 获取会话密钥失败 | 无法获取session_key |
| 400 | 数据签名验证失败 | 解密后的数据签名验证失败 |
| 401 | 无效的认证头 | Authorization头部格式错误 |
| 401 | 登录已过期 | JWT token已过期 |
| 401 | 无效的token | JWT token无效 |
| 500 | 解密用户信息失败 | 解密过程中发生错误 |
| 500 | 获取用户信息失败 | 其他服务器内部错误 |

### 请求示例

```
POST /api/user/info/wx/ HTTP/1.1
Host: api.example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
    "code": "023jm********************3JDqJ3",
    "encryptedData": "CiyLU1**************NFYRPJ/8jftX1Vj+o01...==",
    "iv": "r7Ba********************U3JieA=="
}
```

### 响应示例

```
HTTP/1.1 200 OK
Content-Type: application/json

{
    "success": true,
    "userInfo": {
        "openid": "oXw1G5Jq9xZT6Kcu8L_wP7F2iI0g",
        "nickname": "张三",
        "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/...",
        "gender": 1,
        "country": "中国",
        "province": "广东",
        "city": "深圳"
    }
}
```

## 微信用户信息获取流程说明

在本系统中，微信用户信息的获取分为两个步骤：

1. **登录认证**：通过微信提供的code获取openid
2. **信息更新**：通过小程序前端获取并更新用户信息

### 登录认证流程

1. 小程序前端调用 `wx.login()` 获取临时登录凭证code
2. 将code发送到后端的 `/api/login/` 接口
3. 后端使用 AppID、AppSecret 和 code 调用微信官方接口 `https://api.weixin.qq.com/sns/jscode2session` 获取用户openid
4. 后端生成JWT token并返回给前端

### 用户信息获取流程

现在有两种方式获取用户信息：

#### 方式一：前端获取后上传（原方式）

1. 小程序前端调用 `wx.getUserProfile()` 或通过按钮授权方式获取用户信息
2. 前端获取到用户信息后，调用 `/api/update_user_info/` 接口将信息保存到服务器

#### 方式二：解密方式（新增）

1. 小程序前端调用 `wx.login()` 获取临时登录凭证code
2. 小程序前端调用 `wx.getUserProfile()` 获取加密的用户信息(encryptedData和iv)
3. 将code、encryptedData和iv一起发送到 `/api/user/info/wx/` 接口
4. 后端解密数据并更新用户信息，同时返回解密后的用户信息

### 注意事项

1. 由于微信用户隐私保护政策调整，自2021年4月13日起，小程序获取用户信息需要通过 `wx.getUserProfile()` 或用户主动授权
2. 无论使用哪种方式，前端都必须调用 `wx.getUserProfile()` 并由用户授权才能获取用户信息
3. 第二种方式（解密方式）更安全，但需要同时发送新的临时登录凭证code
4. 解密方式会自动更新数据库中的用户信息，无需再调用更新接口

## 相关接口

- [微信小程序登录接口](/api/login/)
- [获取用户VIP信息接口](/api/get_vip_info/)
- [设置用户VIP状态接口](/api/set_vip_status/) 