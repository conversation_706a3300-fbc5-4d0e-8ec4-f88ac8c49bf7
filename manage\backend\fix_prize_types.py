"""
修复奖品类型
"""
import os
import django
import sys

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'car_wiki.settings')
django.setup()

from treasure.models import Prize

def fix_prize_types():
    """修复奖品类型"""
    # 获取所有奖品
    prizes = Prize.objects.all()
    
    # 创建中文类型到代码值的映射
    prize_type_map = {
        # 基本类型
        '赛车': 'car',
        '配件': 'part',
        '道具': 'item',
        '货币': 'currency',
        '装饰': 'costume',
        '动作': 'action',
        '表情': 'emotion',
        '喇叭': 'horn',
        '礼包': 'gift',
        '碎片': 'fragment',
        '卡片': 'card',
        '宠物': 'pet',
        '称号': 'title',
        
        # 更具体的类型映射
        '金币': 'currency',
        '点券': 'currency',
        '钻石': 'currency',
        '经验': 'currency',
        '积分': 'currency',
        
        '赛车碎片': 'fragment',
        '车辆碎片': 'fragment',
        '车碎片': 'fragment',
        
        '宠物碎片': 'fragment',
        '宠物蛋': 'pet',
        
        '改装件': 'part',
        '引擎': 'part',
        '轮胎': 'part',
        '尾翼': 'part',
        '氮气': 'part',
        
        '服装': 'costume',
        '服饰': 'costume',
        '衣服': 'costume',
        '帽子': 'costume',
        '眼镜': 'costume',
        '鞋子': 'costume',
        
        '动作表情': 'action',
        '互动': 'action',
        
        '头像': 'costume',
        '头像框': 'costume',
        '背景': 'costume',
    }
    
    # 统计信息
    total_prizes = prizes.count()
    updated_prizes = 0
    
    print(f"总共有 {total_prizes} 个奖品")
    
    # 修复每个奖品的类型
    for prize in prizes:
        old_type = prize.prize_type
        
        # 根据奖品名称推断类型
        new_type = None
        
        # 尝试直接匹配
        for key, value in prize_type_map.items():
            if key in prize.name:
                new_type = value
                break
        
        # 如果没有匹配到，保持原样
        if not new_type or new_type == old_type:
            continue
        
        # 更新奖品类型
        prize.prize_type = new_type
        prize.save()
        
        print(f"奖品 {prize.name} (ID: {prize.id}) 的类型从 {old_type} 更新为 {new_type}")
        updated_prizes += 1
    
    print(f"\n总共更新了 {updated_prizes} 个奖品的类型 ({updated_prizes / total_prizes * 100:.2f}%)")

if __name__ == "__main__":
    fix_prize_types()
