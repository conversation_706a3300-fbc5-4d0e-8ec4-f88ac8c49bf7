"""
检查奖品与道具的关联
"""
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'car_wiki.settings')
django.setup()

from treasure.models import Prize, PrizeSource, PrizeSourceRelation

def check_prize_relations():
    """检查奖品与道具的关联"""
    # 获取所有奖品
    prizes = Prize.objects.all()

    # 统计信息
    total_prizes = prizes.count()
    prizes_with_relations = 0
    prizes_without_relations = 0

    print(f"总共有 {total_prizes} 个奖品")

    # 检查每个奖品的关联
    for prize in prizes:
        relations = PrizeSourceRelation.objects.filter(prize=prize)
        if relations.exists():
            prizes_with_relations += 1
            sources = [relation.source.name for relation in relations]
            print(f"奖品 {prize.name} (ID: {prize.id}) 关联了 {len(sources)} 个道具: {', '.join(sources)}")
        else:
            prizes_without_relations += 1
            print(f"奖品 {prize.name} (ID: {prize.id}) 没有关联任何道具")

    print(f"\n统计信息:")
    print(f"有关联的奖品: {prizes_with_relations} ({prizes_with_relations / total_prizes * 100:.2f}%)")
    print(f"没有关联的奖品: {prizes_without_relations} ({prizes_without_relations / total_prizes * 100:.2f}%)")

    # 检查是否还存在旧的source_id字段
    cursor = django.db.connection.cursor()
    cursor.execute("PRAGMA table_info(treasure_prize)")
    columns = [column[1] for column in cursor.fetchall()]
    if 'source_id' in columns:
        print("\n发现旧的source_id字段，检查其中的数据:")
        cursor.execute("SELECT COUNT(*) FROM treasure_prize WHERE source_id IS NOT NULL")
        count = cursor.fetchone()[0]
        print(f"有 {count} 个奖品的source_id字段不为空")

        if count > 0:
            print("\n尝试修复数据:")
            # 获取所有有source_id的奖品
            cursor.execute("SELECT id, name, source_id FROM treasure_prize WHERE source_id IS NOT NULL")
            prizes_with_source = cursor.fetchall()

            for prize_id, prize_name, source_id in prizes_with_source:
                try:
                    prize = Prize.objects.get(id=prize_id)
                    source = PrizeSource.objects.get(id=source_id)

                    # 检查是否已经有关联
                    relation, created = PrizeSourceRelation.objects.get_or_create(
                        prize=prize,
                        source=source,
                        defaults={'probability': prize.probability}
                    )

                    if created:
                        print(f"为奖品 {prize_name} (ID: {prize_id}) 创建了与道具 {source.name} (ID: {source_id}) 的关联")
                    else:
                        print(f"奖品 {prize_name} (ID: {prize_id}) 已经与道具 {source.name} (ID: {source_id}) 关联")
                except Prize.DoesNotExist:
                    print(f"找不到ID为 {prize_id} 的奖品")
                except PrizeSource.DoesNotExist:
                    print(f"找不到ID为 {source_id} 的道具")
                except Exception as e:
                    print(f"处理奖品 {prize_name} (ID: {prize_id}) 时出错: {str(e)}")

def fix_prize_relations():
    """修复奖品与道具的关联"""
    # 获取所有没有关联的奖品
    prizes = Prize.objects.all()
    fixed_count = 0

    for prize in prizes:
        # 检查是否已经有关联
        if not PrizeSourceRelation.objects.filter(prize=prize).exists():
            # 尝试查找同名的道具
            sources = PrizeSource.objects.filter(name__icontains=prize.name)
            if sources.exists():
                source = sources.first()
                relation = PrizeSourceRelation.objects.create(
                    prize=prize,
                    source=source,
                    probability=prize.probability
                )
                print(f"为奖品 {prize.name} (ID: {prize.id}) 创建了与道具 {source.name} (ID: {source.id}) 的关联")
                fixed_count += 1

    print(f"\n总共修复了 {fixed_count} 个奖品的关联")

if __name__ == '__main__':
    check_prize_relations()
    print("\n" + "="*50 + "\n")
    fix_prize_relations()
