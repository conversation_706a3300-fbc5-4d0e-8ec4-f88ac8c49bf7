#!/usr/bin/env python
"""
推进计算表数据初始化脚本

使用方法:
1. 在manage/backend目录下运行: python init_propulsion_data.py
2. 或者使用Django管理命令: python manage.py shell < init_propulsion_data.py

此脚本将根据官方数据初始化推进计算表，包括各等级的推进上限和平均提升数值。
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'car_wiki.settings')
django.setup()

from cars.models import PropulsionLevelTable

def init_propulsion_data():
    """初始化推进计算表数据"""
    
    # 推进计算表数据
    propulsion_data = [
        {
            'car_level': 'C/M1',
            'level_1_max': None,  # 无上限
            'level_2_max': 4381,
            'level_3_max': 4959,
            'level_4_max': 5462,
            'level_5_max': None,  # 无上限
            'level_6_max': None,  # 无上限
            'level_7_max': 6244,
            'level_1_diff': 304,  # 7.6 * 40 = 304
            'level_2_diff': 304,  # 7.6 * 40 = 304
            'level_3_diff': 367,  # 9.175 * 40 = 367
            'level_4_diff': 370,  # 9.25 * 40 = 370
            'level_5_diff': 366,  # 9.15 * 40 = 366
            'level_6_diff': 334,  # 8.35 * 40 = 334
            'level_7_diff': 355,  # 8.875 * 40 = 355
            'level_1_avg_increase': 7.6,
            'level_2_avg_increase': 7.6,
            'level_3_avg_increase': 9.175,
            'level_4_avg_increase': 9.25,
            'level_5_avg_increase': 9.15,
            'level_6_avg_increase': 8.35,
            'level_7_avg_increase': 8.875
        },
        {
            'car_level': 'B/M2/L2/R',
            'level_1_max': 4599,
            'level_2_max': 4671,
            'level_3_max': 5179,
            'level_4_max': 5752,
            'level_5_max': None,  # 无上限
            'level_6_max': None,  # 无上限
            'level_7_max': 6621,
            'level_1_diff': 319,  # 7.975 * 40 = 319
            'level_2_diff': 324,  # 8.1 * 40 = 324
            'level_3_diff': 383,  # 9.575 * 40 = 383
            'level_4_diff': 389,  # 9.725 * 40 = 389
            'level_5_diff': 383,  # 9.575 * 40 = 383
            'level_6_diff': 342,  # 8.55 * 40 = 342
            'level_7_diff': 377,  # 9.425 * 40 = 377
            'level_1_avg_increase': 7.975,
            'level_2_avg_increase': 8.1,
            'level_3_avg_increase': 9.575,
            'level_4_avg_increase': 9.725,
            'level_5_avg_increase': 9.575,
            'level_6_avg_increase': 8.55,
            'level_7_avg_increase': 9.425
        },
        {
            'car_level': 'T1',
            'level_1_max': 4682,
            'level_2_max': 4736,
            'level_3_max': None,  # 无上限
            'level_4_max': 5900,
            'level_5_max': None,  # 无上限
            'level_6_max': None,  # 无上限
            'level_7_max': 6809,
            'level_1_diff': 325,  # 8.125 * 40 = 325
            'level_2_diff': 329,  # 8.225 * 40 = 329
            'level_3_diff': 397,  # 9.925 * 40 = 397
            'level_4_diff': 400,  # 10 * 40 = 400
            'level_5_diff': 396,  # 9.9 * 40 = 396
            'level_6_diff': 360,  # 9 * 40 = 360
            'level_7_diff': 388,  # 9.7 * 40 = 388
            'level_1_avg_increase': 8.125,
            'level_2_avg_increase': 8.225,
            'level_3_avg_increase': 9.925,
            'level_4_avg_increase': 10.0,
            'level_5_avg_increase': 9.9,
            'level_6_avg_increase': 9.0,
            'level_7_avg_increase': 9.7
        },
        {
            'car_level': 'A/M3/L3',
            'level_1_max': 4709,
            'level_2_max': None,  # 无上限
            'level_3_max': None,  # 无上限
            'level_4_max': 6009,
            'level_5_max': 6500,
            'level_6_max': 6753,
            'level_7_max': 6890,
            'level_1_diff': 327,  # 8.175 * 40 = 327
            'level_2_diff': 342,  # 8.55 * 40 = 342
            'level_3_diff': 400,  # 10 * 40 = 400
            'level_4_diff': 407,  # 10.175 * 40 = 407
            'level_5_diff': 400,  # 10 * 40 = 400
            'level_6_diff': 362,  # 9.05 * 40 = 362
            'level_7_diff': 392,  # 9.8 * 40 = 392
            'level_1_avg_increase': 8.175,
            'level_2_avg_increase': 8.55,
            'level_3_avg_increase': 10.0,
            'level_4_avg_increase': 10.175,
            'level_5_avg_increase': 10.0,
            'level_6_avg_increase': 9.05,
            'level_7_avg_increase': 9.8
        },
        {
            'car_level': 'T2',
            'level_1_max': None,  # 无上限
            'level_2_max': 5038,
            'level_3_max': None,  # 无上限
            'level_4_max': None,  # 无上限
            'level_5_max': None,  # 无上限
            'level_6_max': 6753,
            'level_7_max': 6890,
            'level_1_diff': 331,  # 8.275 * 40 = 331
            'level_2_diff': 350,  # 8.75 * 40 = 350
            'level_3_diff': 400,  # 10 * 40 = 400
            'level_4_diff': 400,  # 10 * 40 = 400
            'level_5_diff': 400,  # 10 * 40 = 400
            'level_6_diff': 362,  # 9.05 * 40 = 362
            'level_7_diff': 392,  # 9.8 * 40 = 392
            'level_1_avg_increase': 8.275,
            'level_2_avg_increase': 8.75,
            'level_3_avg_increase': 10.0,
            'level_4_avg_increase': 10.0,
            'level_5_avg_increase': 10.0,
            'level_6_avg_increase': 9.05,
            'level_7_avg_increase': 9.8
        },
        {
            'car_level': 'T2皮肤',
            'level_1_max': 4764,
            'level_2_max': None,  # 无上限
            'level_3_max': None,  # 无上限
            'level_4_max': 5900,
            'level_5_max': None,  # 无上限
            'level_6_max': 6753,
            'level_7_max': 6890,
            'level_1_diff': 331,  # 8.275 * 40 = 331
            'level_2_diff': 350,  # 8.75 * 40 = 350
            'level_3_diff': 400,  # 10 * 40 = 400
            'level_4_diff': 400,  # 10 * 40 = 400
            'level_5_diff': 400,  # 10 * 40 = 400
            'level_6_diff': 362,  # 9.05 * 40 = 362
            'level_7_diff': 392,  # 9.8 * 40 = 392
            'level_1_avg_increase': 8.275,
            'level_2_avg_increase': 8.75,
            'level_3_avg_increase': 10.0,
            'level_4_avg_increase': 10.0,
            'level_5_avg_increase': 10.0,
            'level_6_avg_increase': 9.05,
            'level_7_avg_increase': 9.8
        }
    ]
    
    print("开始初始化推进计算表数据...")
    
    created_count = 0
    updated_count = 0
    
    for data in propulsion_data:
        car_level = data['car_level']
        try:
            obj, created = PropulsionLevelTable.objects.update_or_create(
                car_level=car_level,
                defaults=data
            )
            
            if created:
                created_count += 1
                print(f"✅ 创建 {car_level} 等级数据")
            else:
                updated_count += 1
                print(f"🔄 更新 {car_level} 等级数据")
                
        except Exception as e:
            print(f"❌ 处理 {car_level} 等级时出错: {str(e)}")
    
    print(f"\n📊 初始化完成:")
    print(f"   - 创建: {created_count} 条记录")
    print(f"   - 更新: {updated_count} 条记录")
    print(f"   - 总计: {created_count + updated_count} 条记录")
    
    # 验证数据
    print(f"\n🔍 验证数据:")
    total_records = PropulsionLevelTable.objects.count()
    print(f"   - 数据库中共有 {total_records} 条推进计算表记录")
    
    for level in ['C/M1', 'B/M2/L2/R', 'T1', 'A/M3/L3', 'T2', 'T2皮肤']:
        exists = PropulsionLevelTable.objects.filter(car_level=level).exists()
        status = "✅" if exists else "❌"
        print(f"   - {level}: {status}")

if __name__ == '__main__':
    init_propulsion_data()
