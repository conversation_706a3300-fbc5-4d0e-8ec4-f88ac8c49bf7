"""
修复奖品与道具的关联
"""
import os
import django
import sys

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'car_wiki.settings')
django.setup()

from django.db import connection
from treasure.models import Prize, PrizeSource, PrizeSourceRelation

def check_source_id_field():
    """检查是否存在source_id字段"""
    with connection.cursor() as cursor:
        # 检查SQLite数据库中的表结构
        cursor.execute("PRAGMA table_info(treasure_prize)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'source_id' in columns:
            print("发现source_id字段")
            return True
        else:
            print("没有找到source_id字段")
            return False

def migrate_from_source_id():
    """从source_id字段迁移数据到关联表"""
    if not check_source_id_field():
        return
    
    with connection.cursor() as cursor:
        # 获取所有有source_id的奖品
        cursor.execute("SELECT id, name, source_id FROM treasure_prize WHERE source_id IS NOT NULL")
        prizes_with_source = cursor.fetchall()
        
        print(f"找到 {len(prizes_with_source)} 个有source_id的奖品")
        
        for prize_id, prize_name, source_id in prizes_with_source:
            try:
                prize = Prize.objects.get(id=prize_id)
                source = PrizeSource.objects.get(id=source_id)
                
                # 检查是否已经有关联
                relation, created = PrizeSourceRelation.objects.get_or_create(
                    prize=prize,
                    source=source
                )
                
                if created:
                    print(f"为奖品 {prize_name} (ID: {prize_id}) 创建了与道具 {source.name} (ID: {source_id}) 的关联")
                else:
                    print(f"奖品 {prize_name} (ID: {prize_id}) 已经与道具 {source.name} (ID: {source_id}) 关联")
            except Prize.DoesNotExist:
                print(f"找不到ID为 {prize_id} 的奖品")
            except PrizeSource.DoesNotExist:
                print(f"找不到ID为 {source_id} 的道具")
            except Exception as e:
                print(f"处理奖品 {prize_name} (ID: {prize_id}) 时出错: {str(e)}")

def create_default_relations():
    """为没有关联的奖品创建默认关联"""
    # 获取所有奖品
    prizes = Prize.objects.all()
    
    # 获取或创建默认道具
    default_source, created = PrizeSource.objects.get_or_create(
        name="默认道具",
        defaults={
            'source_code': "DEFAULT_SOURCE",
            'source_type': "item",
            'description': "系统自动创建的默认道具，用于关联没有明确来源的奖品",
            'is_active': True
        }
    )
    
    if created:
        print(f"创建了默认道具: {default_source.name} (ID: {default_source.id})")
    else:
        print(f"使用已有的默认道具: {default_source.name} (ID: {default_source.id})")
    
    # 为没有关联的奖品创建关联
    count = 0
    for prize in prizes:
        if not PrizeSourceRelation.objects.filter(prize=prize).exists():
            relation = PrizeSourceRelation.objects.create(
                prize=prize,
                source=default_source
            )
            count += 1
            print(f"为奖品 {prize.name} (ID: {prize.id}) 创建了与默认道具的关联")
    
    print(f"总共为 {count} 个奖品创建了默认关联")

def main():
    """主函数"""
    print("开始修复奖品与道具的关联...")
    
    # 从source_id字段迁移数据
    migrate_from_source_id()
    
    # 为没有关联的奖品创建默认关联
    create_default_relations()
    
    print("修复完成")

if __name__ == "__main__":
    main()
